"""Main window for Photo Center application."""

import sys
from pathlib import Path

# Setup warning suppression before any ML imports
from ..utils.warnings_suppressor import setup_quiet_environment
setup_quiet_environment()

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFileDialog, QProgressBar, QTextEdit,
    QGroupBox, QGridLayout, QDoubleSpinBox, QComboBox,
    QCheckBox, QTabWidget, QSplitter, QMessageBox
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

from ..utils.config import Config
from ..utils.logger import get_logger
from ..models.unified_detector import UnifiedHumanDetector
from ..image_processing.raw_processor import RawProcessor
from ..image_processing.centering import PhotoCenterer
from ..image_processing.crop_centering import <PERSON>rop<PERSON>enterer
from ..batch.batch_processor import BatchProcessor
from .preview_widget import PreviewWidget


class BatchProcessingThread(QThread):
    """Thread for batch processing operations."""
    
    progress_updated = Signal(int, int)  # current, total
    processing_finished = Signal(object)  # BatchResult
    error_occurred = Signal(str)
    
    def __init__(self, batch_processor, input_dir, output_dir, recursive=True):
        super().__init__()
        self.batch_processor = batch_processor
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.recursive = recursive
    
    def run(self):
        """Run batch processing in thread."""
        try:
            def progress_callback(current, total):
                self.progress_updated.emit(current, total)
            
            result = self.batch_processor.process_directory(
                self.input_dir,
                self.output_dir,
                self.recursive,
                progress_callback
            )
            self.processing_finished.emit(result)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.config = Config()
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.human_detector = None
        self.raw_processor = None
        self.photo_centerer = None
        self.crop_centerer = None
        self.batch_processor = None
        
        # UI components
        self.preview_widget = None
        self.progress_bar = None
        self.log_text = None
        self.batch_thread = None
        
        self.init_ui()
        self.init_components()
    
    def init_ui(self):
        """Initialize user interface."""
        self.setWindowTitle("Photo Center - Intelligent Photo Centering")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Controls
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Preview and logs
        right_panel = self.create_preview_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Status bar
        self.statusBar().showMessage("Ready")
    
    def create_control_panel(self) -> QWidget:
        """Create the control panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Single image processing
        single_group = QGroupBox("Single Image Processing")
        single_layout = QVBoxLayout(single_group)
        
        self.load_image_btn = QPushButton("Load Image")
        self.load_image_btn.clicked.connect(self.load_single_image)
        single_layout.addWidget(self.load_image_btn)
        
        self.process_image_btn = QPushButton("Process Image")
        self.process_image_btn.clicked.connect(self.process_single_image)
        self.process_image_btn.setEnabled(False)
        single_layout.addWidget(self.process_image_btn)
        
        self.save_result_btn = QPushButton("Save Result")
        self.save_result_btn.clicked.connect(self.save_single_result)
        self.save_result_btn.setEnabled(False)
        single_layout.addWidget(self.save_result_btn)
        
        layout.addWidget(single_group)
        
        # Batch processing
        batch_group = QGroupBox("Batch Processing")
        batch_layout = QVBoxLayout(batch_group)
        
        self.select_input_dir_btn = QPushButton("Select Input Directory")
        self.select_input_dir_btn.clicked.connect(self.select_input_directory)
        batch_layout.addWidget(self.select_input_dir_btn)
        
        self.select_output_dir_btn = QPushButton("Select Output Directory")
        self.select_output_dir_btn.clicked.connect(self.select_output_directory)
        batch_layout.addWidget(self.select_output_dir_btn)
        
        self.recursive_checkbox = QCheckBox("Process subdirectories recursively")
        self.recursive_checkbox.setChecked(True)
        batch_layout.addWidget(self.recursive_checkbox)
        
        self.start_batch_btn = QPushButton("Start Batch Processing")
        self.start_batch_btn.clicked.connect(self.start_batch_processing)
        batch_layout.addWidget(self.start_batch_btn)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        batch_layout.addWidget(self.progress_bar)
        
        layout.addWidget(batch_group)
        
        # Settings
        settings_group = self.create_settings_panel()
        layout.addWidget(settings_group)
        
        # Stretch
        layout.addStretch()
        
        return panel
    
    def create_settings_panel(self) -> QGroupBox:
        """Create the settings panel."""
        group = QGroupBox("Settings")
        layout = QGridLayout(group)

        row = 0

        # Detection model selection
        layout.addWidget(QLabel("Detection Model:"), row, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["yolo", "openpose", "auto"])
        current_model = self.config.detection_model
        self.model_combo.setCurrentText(current_model)
        self.model_combo.currentTextChanged.connect(self.update_detection_model)
        layout.addWidget(self.model_combo, row, 1)
        row += 1

        # Confidence threshold
        layout.addWidget(QLabel("Confidence Threshold:"), row, 0)
        self.confidence_spin = QDoubleSpinBox()
        self.confidence_spin.setRange(0.1, 1.0)
        self.confidence_spin.setSingleStep(0.1)
        self.confidence_spin.setValue(self.config.model_confidence_threshold)
        self.confidence_spin.valueChanged.connect(self.update_confidence_threshold)
        layout.addWidget(self.confidence_spin, row, 1)
        row += 1

        # Centering method
        layout.addWidget(QLabel("Centering Method:"), row, 0)
        self.centering_method_combo = QComboBox()
        self.centering_method_combo.addItems([
            "face_chest_based",
            "keypoint_based",
            "bbox_based",
            "center_of_mass"
        ])
        current_method = self.config.get('centering.method', 'face_chest_based')
        self.centering_method_combo.setCurrentText(current_method)
        self.centering_method_combo.currentTextChanged.connect(self.update_centering_method)
        layout.addWidget(self.centering_method_combo, row, 1)
        row += 1

        # Face weight
        layout.addWidget(QLabel("Face Weight:"), row, 0)
        self.face_weight_spin = QDoubleSpinBox()
        self.face_weight_spin.setRange(0.0, 1.0)
        self.face_weight_spin.setSingleStep(0.1)
        self.face_weight_spin.setDecimals(1)
        self.face_weight_spin.setValue(self.config.face_weight)
        self.face_weight_spin.valueChanged.connect(self.update_weights)
        layout.addWidget(self.face_weight_spin, row, 1)
        row += 1

        # Chest weight
        layout.addWidget(QLabel("Chest Weight:"), row, 0)
        self.chest_weight_spin = QDoubleSpinBox()
        self.chest_weight_spin.setRange(0.0, 1.0)
        self.chest_weight_spin.setSingleStep(0.1)
        self.chest_weight_spin.setDecimals(1)
        self.chest_weight_spin.setValue(self.config.chest_weight)
        self.chest_weight_spin.valueChanged.connect(self.update_weights)
        layout.addWidget(self.chest_weight_spin, row, 1)
        row += 1

        # Hip weight
        layout.addWidget(QLabel("Hip Weight:"), row, 0)
        self.hip_weight_spin = QDoubleSpinBox()
        self.hip_weight_spin.setRange(0.0, 1.0)
        self.hip_weight_spin.setSingleStep(0.1)
        self.hip_weight_spin.setDecimals(1)
        self.hip_weight_spin.setValue(self.config.hip_weight)
        self.hip_weight_spin.valueChanged.connect(self.update_weights)
        layout.addWidget(self.hip_weight_spin, row, 1)
        row += 1
        
        # Output format
        layout.addWidget(QLabel("Output Format:"), row, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["jpg", "png", "tiff"])
        self.format_combo.setCurrentText(self.config.output_format)
        self.format_combo.currentTextChanged.connect(self.update_output_format)
        layout.addWidget(self.format_combo, row, 1)
        row += 1
        
        return group

    def create_preview_panel(self) -> QWidget:
        """Create the preview panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Tab widget for different views
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # Preview tab
        self.preview_widget = PreviewWidget()
        tab_widget.addTab(self.preview_widget, "Preview")

        # Log tab
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        tab_widget.addTab(self.log_text, "Logs")

        return panel

    def init_components(self):
        """Initialize processing components."""
        try:
            self.human_detector = UnifiedHumanDetector(self.config)
            self.raw_processor = RawProcessor(self.config)
            self.photo_centerer = PhotoCenterer(self.config)
            self.crop_centerer = CropCenterer(self.config)
            self.batch_processor = BatchProcessor(self.config)

            self.log_message("Components initialized successfully")
            self.log_message(f"Active detection model: {self.human_detector.get_current_model_type()}")

        except Exception as e:
            self.log_message(f"Error initializing components: {e}")
            QMessageBox.critical(self, "Initialization Error",
                               f"Failed to initialize components:\n{e}")

    def log_message(self, message: str):
        """Add message to log."""
        if self.log_text:
            self.log_text.append(message)
            self.log_text.ensureCursorVisible()
        self.logger.info(message)

    # Settings update methods
    def update_detection_model(self, model_type: str):
        """Update detection model setting."""
        self.config.set('models.detection_model', model_type)
        if hasattr(self, 'human_detector') and self.human_detector:
            self.human_detector.switch_model(model_type)
        self.log_message(f"Detection model updated to {model_type}")

    def update_confidence_threshold(self, value: float):
        """Update confidence threshold setting."""
        self.config.set('models.human_detection.confidence_threshold', value)
        self.log_message(f"Confidence threshold updated to {value}")

    def update_target_position(self):
        """Update target position setting."""
        x = self.target_x_spin.value()
        y = self.target_y_spin.value()
        self.config.set('centering.target_position', [x, y])
        self.log_message(f"Target position updated to ({x}, {y})")

    def update_centering_method(self, method: str):
        """Update centering method setting."""
        self.config.set('centering.method', method)
        self.log_message(f"Centering method updated to {method}")

    def update_output_format(self, format_str: str):
        """Update output format setting."""
        self.config.set('image_processing.output_format', format_str)
        self.log_message(f"Output format updated to {format_str}")

    def update_margin_ratio(self, value: float):
        """Update margin ratio setting."""
        self.config.set('centering.margin_ratio', value)
        self.log_message(f"Margin ratio updated to {value}")

    def update_weights(self):
        """Update face, chest, and hip weights."""
        face_weight = self.face_weight_spin.value()
        chest_weight = self.chest_weight_spin.value()
        hip_weight = self.hip_weight_spin.value()

        self.config.set('centering.face_weight', face_weight)
        self.config.set('centering.chest_weight', chest_weight)
        self.config.set('centering.hip_weight', hip_weight)

        self.log_message(f"Weights updated - Face: {face_weight}, Chest: {chest_weight}, Hip: {hip_weight}")

        # Reinitialize photo centerer with new config
        self.photo_centerer = PhotoCenterer(self.config)

    # Single image processing methods
    def load_single_image(self):
        """Load a single image for processing."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Image File",
            "",
            "Image Files (*.jpg *.jpeg *.png *.tiff *.cr2 *.nef *.arw *.dng *.raw);;All Files (*)"
        )

        if file_path:
            try:
                self.current_image_path = file_path
                image = self.raw_processor.load_image(file_path)
                self.current_image = image

                # Show original image in preview
                self.preview_widget.set_original_image(image)

                self.process_image_btn.setEnabled(True)
                self.process_image_btn.setText("Process Image")  # Reset button text for new image
                self.log_message(f"Loaded image: {file_path}")

            except Exception as e:
                self.log_message(f"Error loading image: {e}")
                QMessageBox.critical(self, "Load Error", f"Failed to load image:\n{e}")

    def process_single_image(self):
        """Process the currently loaded image."""
        if not hasattr(self, 'current_image'):
            return

        try:
            # Reinitialize centerers to pick up any config changes
            self.photo_centerer = PhotoCenterer(self.config)
            self.crop_centerer = CropCenterer(self.config)

            # Detect humans
            detections = self.human_detector.detect_humans(self.current_image)

            if not detections:
                QMessageBox.warning(self, "No Detection", "No human detected in the image.")
                return

            # Get best detection (pass image shape for better center-person selection)
            image_shape = self.current_image.shape[:2]  # (height, width)
            best_detection = self.human_detector.get_best_detection(detections, image_shape)

            # Log detection info for graduation photos
            if len(detections) > 1:
                self.log_message(f"Found {len(detections)} people, selected most centered person")
            else:
                self.log_message(f"Found 1 person")

            # Center the subject using traditional method
            centering_result = self.photo_centerer.center_subject(
                self.current_image, best_detection
            )

            # Also generate crop centering result for better left-right centering
            crop_result = self.crop_centerer.center_subject_with_crop(
                self.current_image, best_detection, centering_method='face_chest_based'
            )

            # Store results
            self.current_result = centering_result
            self.current_crop_result = crop_result

            # Show result in preview
            self.preview_widget.set_processed_image(
                centering_result.cropped_image,
                best_detection,
                centering_result
            )

            # Also set the crop result for new preview modes
            self.preview_widget.set_crop_result(crop_result)

            self.save_result_btn.setEnabled(True)

            # Change button text to "Reprocess Image" after first processing
            self.process_image_btn.setText("Reprocess Image")

            self.log_message(
                f"Processing completed - Detection confidence: {best_detection['confidence']:.3f}, "
                f"Centering confidence: {centering_result.confidence:.3f}, "
                f"Method: {centering_result.method_used}, "
                f"Crop confidence: {crop_result.confidence:.3f}, "
                f"Crop ratio: {crop_result.crop_ratio:.3f}"
            )

        except Exception as e:
            self.log_message(f"Error processing image: {e}")
            QMessageBox.critical(self, "Processing Error", f"Failed to process image:\n{e}")

    def save_single_result(self):
        """Save the processed image result."""
        if not hasattr(self, 'current_result'):
            return

        # Determine default filename and filter based on original file
        default_filename = ""
        file_filter = "JPEG Files (*.jpg);;PNG Files (*.png);;TIFF Files (*.tiff);;RAW Files (*.cr2 *.nef *.arw *.dng *.raw);;All Files (*)"

        if hasattr(self, 'current_image_path') and self.current_image_path:
            original_path = Path(self.current_image_path)
            original_ext = original_path.suffix.lower()

            # Create default filename with original extension
            default_filename = f"{original_path.stem}_centered{original_ext}"

            # Adjust filter to prioritize the original format
            if original_ext in ['.jpg', '.jpeg']:
                file_filter = "JPEG Files (*.jpg);;PNG Files (*.png);;TIFF Files (*.tiff);;RAW Files (*.cr2 *.nef *.arw *.dng *.raw);;All Files (*)"
            elif original_ext == '.png':
                file_filter = "PNG Files (*.png);;JPEG Files (*.jpg);;TIFF Files (*.tiff);;RAW Files (*.cr2 *.nef *.arw *.dng *.raw);;All Files (*)"
            elif original_ext in ['.tiff', '.tif']:
                file_filter = "TIFF Files (*.tiff);;JPEG Files (*.jpg);;PNG Files (*.png);;RAW Files (*.cr2 *.nef *.arw *.dng *.raw);;All Files (*)"
            elif original_ext in ['.cr2', '.nef', '.arw', '.dng', '.raw', '.orf', '.rw2', '.pef', '.srw']:
                file_filter = "TIFF Files (*.tiff);;JPEG Files (*.jpg);;PNG Files (*.png);;All Files (*)"
                # For RAW files, suggest TIFF format with a descriptive filename
                default_filename = f"{original_path.stem}_centered_from_{original_ext[1:]}.tiff"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Processed Image",
            default_filename,
            file_filter
        )

        if file_path:
            try:
                # Pass original path to preserve format
                original_path = getattr(self, 'current_image_path', None)
                self.raw_processor.save_image(
                    self.current_result.cropped_image,
                    file_path,
                    original_path=original_path
                )
                self.log_message(f"Result saved to: {file_path}")

            except Exception as e:
                self.log_message(f"Error saving result: {e}")
                QMessageBox.critical(self, "Save Error", f"Failed to save result:\n{e}")

    # Batch processing methods
    def select_input_directory(self):
        """Select input directory for batch processing."""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "Select Input Directory"
        )

        if dir_path:
            self.input_directory = dir_path
            self.select_input_dir_btn.setText(f"Input: {Path(dir_path).name}")
            self.log_message(f"Input directory selected: {dir_path}")

    def select_output_directory(self):
        """Select output directory for batch processing."""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory"
        )

        if dir_path:
            self.output_directory = dir_path
            self.select_output_dir_btn.setText(f"Output: {Path(dir_path).name}")
            self.log_message(f"Output directory selected: {dir_path}")

    def start_batch_processing(self):
        """Start batch processing operation."""
        if not hasattr(self, 'input_directory'):
            QMessageBox.warning(self, "No Input Directory", "Please select an input directory first.")
            return

        output_dir = getattr(self, 'output_directory', None)
        recursive = self.recursive_checkbox.isChecked()

        # Disable UI during processing
        self.start_batch_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Start processing thread
        self.batch_thread = BatchProcessingThread(
            self.batch_processor,
            self.input_directory,
            output_dir,
            recursive
        )

        self.batch_thread.progress_updated.connect(self.update_batch_progress)
        self.batch_thread.processing_finished.connect(self.batch_processing_finished)
        self.batch_thread.error_occurred.connect(self.batch_processing_error)

        self.batch_thread.start()

        self.log_message("Batch processing started...")

    def update_batch_progress(self, current: int, total: int):
        """Update batch processing progress."""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.statusBar().showMessage(f"Processing: {current}/{total}")

    def batch_processing_finished(self, result):
        """Handle batch processing completion."""
        self.start_batch_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("Ready")

        # Show results
        message = (
            f"Batch processing completed!\n\n"
            f"Total files: {result.total_files}\n"
            f"Processed: {result.processed_files}\n"
            f"Failed: {result.failed_files}\n"
            f"Skipped: {result.skipped_files}\n"
            f"Processing time: {result.processing_time:.2f} seconds"
        )

        QMessageBox.information(self, "Batch Processing Complete", message)
        self.log_message(message.replace('\n\n', ' - ').replace('\n', ', '))

    def batch_processing_error(self, error_message: str):
        """Handle batch processing error."""
        self.start_batch_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("Ready")

        QMessageBox.critical(self, "Batch Processing Error", f"Error during batch processing:\n{error_message}")
        self.log_message(f"Batch processing error: {error_message}")

    def closeEvent(self, event):
        """Handle application close event."""
        if self.batch_thread and self.batch_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Processing in Progress",
                "Batch processing is still running. Do you want to quit anyway?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.batch_thread.terminate()
                self.batch_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """Main entry point for the UI application."""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Photo Center")
    app.setApplicationVersion("0.1.0")
    app.setOrganizationName("Photo Center Team")

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
